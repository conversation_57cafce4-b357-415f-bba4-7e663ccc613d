import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { useNotification } from '../../contexts/NotificationContext';
import Pagination from '../common/Pagination';
import './Assignments.css';

interface Schedule {
  id: string;
  title: string;
  description: string;
  labId: string;
  labName: string;
  classId: string;
  className: string;
  instructorName: string;
  scheduledDate: string;
  durationMinutes: number;
  deadline: string;
  status: string;
  assignmentType: string;
  createdAt: string;
}

interface Assignment {
  id: string;
  scheduleId: string;
  scheduleTitle: string;
  description: string;
  labName: string;
  className: string;
  instructorName: string;
  scheduledDate: string;
  durationMinutes: number;
  status: string;
  assignmentType: 'group' | 'individual' | 'class';
  groupId?: string;
  groupName?: string;
  userId?: string;
  studentName?: string;
  assignedAt: string;
}

const AssignmentManagement: React.FC = () => {
  const { user } = useAuth();
  const { showSuccess, showError } = useNotification();
  const [schedules, setSchedules] = useState<Schedule[]>([]);
  const [assignments, setAssignments] = useState<Assignment[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [classFilter, setClassFilter] = useState('');
  const [groupFilter, setGroupFilter] = useState('');
  const [assignmentTypeFilter, setAssignmentTypeFilter] = useState('');
  const [classes, setClasses] = useState<any[]>([]);
  const [groups, setGroups] = useState<any[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [viewMode, setViewMode] = useState<'schedules' | 'assignments'>('schedules');

  // Modal states
  const [showAssignModal, setShowAssignModal] = useState(false);
  const [selectedSchedule, setSelectedSchedule] = useState<Schedule | null>(null);
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingAssignment, setEditingAssignment] = useState<Assignment | null>(null);

  useEffect(() => {
    fetchSchedules();
    fetchAssignments();
    fetchClasses();
  }, []);

  useEffect(() => {
    if (classFilter) {
      fetchGroups();
    } else {
      setGroups([]);
    }
  }, [classFilter]);

  const fetchSchedules = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      const response = await fetch('/api/schedules', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setSchedules(data.schedules || []);
      } else {
        console.error('Failed to fetch schedules');
        setSchedules([]);
      }
    } catch (error) {
      console.error('Error fetching schedules:', error);
      setSchedules([]);
    } finally {
      setLoading(false);
    }
  };

  const fetchAssignments = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/assignments', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setAssignments(data.assignments || []);
      } else {
        console.error('Failed to fetch assignments');
        setAssignments([]);
      }
    } catch (error) {
      console.error('Error fetching assignments:', error);
      setAssignments([]);
    }
  };

  const fetchClasses = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/classes', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setClasses(data.classes || []);
      }
    } catch (error) {
      console.error('Error fetching classes:', error);
    }
  };

  const fetchGroups = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`/api/groups?classId=${classFilter}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setGroups(data.groups || []);
      }
    } catch (error) {
      console.error('Error fetching groups:', error);
    }
  };

  const handleAssignSchedule = (schedule: Schedule) => {
    setSelectedSchedule(schedule);
    setShowAssignModal(true);
  };

  const handleEditAssignment = (assignment: Assignment) => {
    setEditingAssignment(assignment);
    setShowEditModal(true);
  };

  const handleDeleteAssignment = async (assignmentId: string) => {
    if (!window.confirm('Are you sure you want to remove this assignment? This action cannot be undone.')) {
      return;
    }

    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`/api/assignments/${assignmentId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        showSuccess('Assignment Removed', 'Assignment has been removed successfully');
        fetchAssignments();
      } else {
        const errorData = await response.json();
        showError('Delete Failed', errorData.error || 'Failed to remove assignment');
      }
    } catch (error) {
      console.error('Error deleting assignment:', error);
      showError('Delete Failed', 'Failed to remove assignment. Please try again.');
    }
  };

  // Filter data based on current view mode
  const getFilteredData = () => {
    if (viewMode === 'schedules') {
      return schedules.filter(schedule => {
        const matchesSearch = schedule.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                             schedule.description.toLowerCase().includes(searchTerm.toLowerCase());
        const matchesClass = !classFilter || schedule.classId === classFilter;
        return matchesSearch && matchesClass;
      });
    } else {
      return assignments.filter(assignment => {
        const matchesSearch = assignment.scheduleTitle.toLowerCase().includes(searchTerm.toLowerCase()) ||
                             assignment.description.toLowerCase().includes(searchTerm.toLowerCase());
        const matchesClass = !classFilter || assignment.className === classes.find(c => c.id === classFilter)?.name;
        const matchesGroup = !groupFilter || assignment.groupId === groupFilter;
        const matchesType = !assignmentTypeFilter || assignment.assignmentType === assignmentTypeFilter;
        return matchesSearch && matchesClass && matchesGroup && matchesType;
      });
    }
  };

  const filteredData = getFilteredData();

  // Pagination
  const totalPages = Math.ceil(filteredData.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedData = filteredData.slice(startIndex, startIndex + itemsPerPage);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled': return '#007bff';
      case 'in_progress': return '#28a745';
      case 'completed': return '#6c757d';
      case 'cancelled': return '#dc3545';
      case 'assigned': return '#17a2b8';
      default: return '#6c757d';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  if (loading) {
    return (
      <div className="assignments">
        <div className="loading-container">
          <div className="loading-spinner">Loading assignment management...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="assignments">
      <div className="assignments-header">
        <h1>Assignment Management</h1>
        <p>Assign existing assignments to groups, individuals, or entire classes. Create basic assignments in Assignment Creation first.</p>
      </div>

      {/* View Mode Toggle */}
      <div className="view-mode-toggle">
        <button
          className={`btn ${viewMode === 'schedules' ? 'btn-primary' : 'btn-secondary'}`}
          onClick={() => setViewMode('schedules')}
        >
          Available Assignments
        </button>
        <button
          className={`btn ${viewMode === 'assignments' ? 'btn-primary' : 'btn-secondary'}`}
          onClick={() => setViewMode('assignments')}
        >
          Current Assignments
        </button>
      </div>

      {/* Search and Filters */}
      <div className="assignments-controls">
        <div className="search-section">
          <input
            type="text"
            placeholder="Search assignments..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="search-input"
          />
        </div>

        <div className="filter-group">
          <label htmlFor="classFilter">Filter by Class:</label>
          <select
            id="classFilter"
            value={classFilter}
            onChange={(e) => setClassFilter(e.target.value)}
            className="filter-select"
          >
            <option value="">All Classes</option>
            {classes.map(cls => (
              <option key={cls.id} value={cls.id}>{cls.name}</option>
            ))}
          </select>
        </div>

        {viewMode === 'assignments' && (
          <>
            <div className="filter-group">
              <label htmlFor="groupFilter">Filter by Group:</label>
              <select
                id="groupFilter"
                value={groupFilter}
                onChange={(e) => setGroupFilter(e.target.value)}
                className="filter-select"
                disabled={!classFilter}
              >
                <option value="">All Groups</option>
                {groups.map(group => (
                  <option key={group.id} value={group.id}>{group.name}</option>
                ))}
              </select>
            </div>

            <div className="filter-group">
              <label htmlFor="assignmentTypeFilter">Filter by Type:</label>
              <select
                id="assignmentTypeFilter"
                value={assignmentTypeFilter}
                onChange={(e) => setAssignmentTypeFilter(e.target.value)}
                className="filter-select"
              >
                <option value="">All Types</option>
                <option value="class">Class Assignment</option>
                <option value="group">Group Assignment</option>
                <option value="individual">Individual Assignment</option>
              </select>
            </div>
          </>
        )}
      </div>

      {/* Data Table */}
      <div className="assignments-table-container">
        <table className="assignments-table">
          <thead>
            <tr>
              <th>Assignment</th>
              <th>Lab</th>
              <th>Class</th>
              {viewMode === 'assignments' && <th>Assigned To</th>}
              <th>Scheduled Date</th>
              <th>Duration</th>
              <th>Status</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {paginatedData.length === 0 ? (
              <tr>
                <td colSpan={viewMode === 'assignments' ? 8 : 7} className="no-assignments">
                  <div className="no-data">
                    <h3>No {viewMode === 'schedules' ? 'available assignments' : 'assignments'} found</h3>
                    <p>No {viewMode === 'schedules' ? 'available assignments' : 'assignments'} match your current filters.</p>
                  </div>
                </td>
              </tr>
            ) : (
              paginatedData.map((item: any) => (
                <tr key={item.id} className="assignment-row">
                  <td className="assignment-title">
                    <div className="title-cell">
                      <h4>{viewMode === 'schedules' ? item.title : item.scheduleTitle}</h4>
                      {item.description && <p>{item.description}</p>}
                    </div>
                  </td>
                  <td>{item.labName}</td>
                  <td>{item.className || 'Not assigned'}</td>
                  {viewMode === 'assignments' && (
                    <td>
                      {item.assignmentType === 'class' ? 'Entire Class' :
                       item.assignmentType === 'group' ? item.groupName :
                       item.studentName}
                    </td>
                  )}
                  <td>
                    <div>
                      <div>{formatDate(item.scheduledDate)}</div>
                      <small>{formatTime(item.scheduledDate)}</small>
                    </div>
                  </td>
                  <td>{item.durationMinutes} min</td>
                  <td>
                    <span
                      className="status-badge"
                      style={{ backgroundColor: getStatusColor(item.status) }}
                    >
                      {item.status.replace('_', ' ').toUpperCase()}
                    </span>
                  </td>
                  <td className="assignment-actions">
                    {viewMode === 'schedules' ? (
                      <button
                        className="btn btn-sm btn-primary"
                        onClick={() => handleAssignSchedule(item)}
                        title="Assign to Groups/Students"
                      >
                        📋 Assign
                      </button>
                    ) : (
                      <>
                        <button
                          className="btn btn-sm btn-primary"
                          onClick={() => handleEditAssignment(item)}
                          title="Edit Assignment"
                        >
                          ✏️ Edit
                        </button>
                        <button
                          className="btn btn-sm btn-danger"
                          onClick={() => handleDeleteAssignment(item.id)}
                          title="Remove Assignment"
                        >
                          🗑️ Remove
                        </button>
                      </>
                    )}
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {filteredData.length > 0 && (
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          totalItems={filteredData.length}
          itemsPerPage={itemsPerPage}
          onPageChange={setCurrentPage}
          onItemsPerPageChange={(newItemsPerPage) => {
            setItemsPerPage(newItemsPerPage);
            setCurrentPage(1);
          }}
          showItemsPerPage={true}
          showJumpToPage={true}
          itemsPerPageOptions={[5, 10, 20, 50]}
        />
      )}

      {/* Assign Assignment Modal */}
      {showAssignModal && selectedSchedule && (
        <AssignAssignmentModal
          schedule={selectedSchedule}
          onClose={() => {
            setShowAssignModal(false);
            setSelectedSchedule(null);
          }}
          onSuccess={() => {
            setShowAssignModal(false);
            setSelectedSchedule(null);
            fetchAssignments();
            showSuccess('Assignment Created', 'Assignment has been assigned successfully');
          }}
          classes={classes}
          groups={groups}
        />
      )}

      {/* Edit Assignment Modal */}
      {showEditModal && editingAssignment && (
        <EditAssignmentModal
          assignment={editingAssignment}
          onClose={() => {
            setShowEditModal(false);
            setEditingAssignment(null);
          }}
          onSuccess={() => {
            setShowEditModal(false);
            setEditingAssignment(null);
            fetchAssignments();
            showSuccess('Assignment Updated', 'Assignment has been updated successfully');
          }}
          classes={classes}
          groups={groups}
        />
      )}
    </div>
  );
};

// Assign Assignment Modal Component
interface AssignAssignmentModalProps {
  schedule: Schedule;
  onClose: () => void;
  onSuccess: () => void;
  classes: any[];
  groups: any[];
}

const AssignAssignmentModal: React.FC<AssignAssignmentModalProps> = ({
  schedule,
  onClose,
  onSuccess,
  classes,
  groups
}) => {
  const { showError } = useNotification();
  const [loading, setLoading] = useState(false);
  const [assignmentType, setAssignmentType] = useState<'class' | 'group' | 'individual'>('class');
  const [selectedClass, setSelectedClass] = useState(schedule.classId || '');
  const [selectedGroups, setSelectedGroups] = useState<string[]>([]);
  const [selectedStudents, setSelectedStudents] = useState<string[]>([]);
  const [students, setStudents] = useState<any[]>([]);
  const [classGroups, setClassGroups] = useState<any[]>([]);

  useEffect(() => {
    if (selectedClass) {
      fetchStudents();
      fetchClassGroups();
    }
  }, [selectedClass]);

  const fetchStudents = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`/api/users?role=student&classId=${selectedClass}`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      if (response.ok) {
        const data = await response.json();
        setStudents(data.users || []);
      }
    } catch (error) {
      console.error('Error fetching students:', error);
    }
  };

  const fetchClassGroups = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`/api/groups?classId=${selectedClass}`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      if (response.ok) {
        const data = await response.json();
        setClassGroups(data.groups || []);
      }
    } catch (error) {
      console.error('Error fetching groups:', error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const token = localStorage.getItem('token');

      if (assignmentType === 'class') {
        // Assign to entire class
        const response = await fetch('/api/assignments', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify({
            schedule_id: schedule.id,
            class_id: selectedClass,
            assignment_type: 'class'
          })
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to create class assignment');
        }
      } else if (assignmentType === 'group' && selectedGroups.length > 0) {
        // Assign to selected groups
        for (const groupId of selectedGroups) {
          const response = await fetch('/api/assignments', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify({
              schedule_id: schedule.id,
              group_id: groupId,
              assignment_type: 'group'
            })
          });

          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || 'Failed to create group assignment');
          }
        }
      } else if (assignmentType === 'individual' && selectedStudents.length > 0) {
        // Assign to selected students
        for (const studentId of selectedStudents) {
          const response = await fetch('/api/assignments', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify({
              schedule_id: schedule.id,
              user_id: studentId,
              assignment_type: 'individual'
            })
          });

          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || 'Failed to create individual assignment');
          }
        }
      }

      onSuccess();
    } catch (error) {
      console.error('Error creating assignment:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to create assignment. Please try again.';
      showError('Assignment Failed', errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="modal assign-assignment-modal" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h2>Assign Assignment: {schedule.title}</h2>
          <button className="close-btn" onClick={onClose}>×</button>
        </div>

        <form onSubmit={handleSubmit} className="modal-content">
          <div className="form-section">
            <h3>Assignment Details</h3>
            <div className="assignment-info">
              <p><strong>Lab:</strong> {schedule.labName}</p>
              <p><strong>Date:</strong> {new Date(schedule.scheduledDate).toLocaleDateString()}</p>
              <p><strong>Duration:</strong> {schedule.durationMinutes} minutes</p>
            </div>
          </div>

          <div className="form-section">
            <h3>Assignment Type</h3>
            <div className="assignment-type-selector">
              <label className="radio-option">
                <input
                  type="radio"
                  name="assignmentType"
                  value="class"
                  checked={assignmentType === 'class'}
                  onChange={(e) => setAssignmentType(e.target.value as 'class')}
                />
                <span>Entire Class</span>
              </label>
              <label className="radio-option">
                <input
                  type="radio"
                  name="assignmentType"
                  value="group"
                  checked={assignmentType === 'group'}
                  onChange={(e) => setAssignmentType(e.target.value as 'group')}
                />
                <span>Specific Groups</span>
              </label>
              <label className="radio-option">
                <input
                  type="radio"
                  name="assignmentType"
                  value="individual"
                  checked={assignmentType === 'individual'}
                  onChange={(e) => setAssignmentType(e.target.value as 'individual')}
                />
                <span>Individual Students</span>
              </label>
            </div>
          </div>

          <div className="form-section">
            <h3>Class Selection</h3>
            <div className="form-group">
              <label htmlFor="classSelect">Select Class *</label>
              <select
                id="classSelect"
                value={selectedClass}
                onChange={(e) => setSelectedClass(e.target.value)}
                required
              >
                <option value="">Select a class</option>
                {classes.map(cls => (
                  <option key={cls.id} value={cls.id}>{cls.name}</option>
                ))}
              </select>
            </div>
          </div>

          {assignmentType === 'group' && selectedClass && (
            <div className="form-section">
              <h3>Select Groups</h3>
              <div className="selection-grid">
                {classGroups.length === 0 ? (
                  <p className="no-data-message">No groups found for this class.</p>
                ) : (
                  classGroups.map(group => (
                    <label key={group.id} className="checkbox-option">
                      <input
                        type="checkbox"
                        checked={selectedGroups.includes(group.id)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setSelectedGroups([...selectedGroups, group.id]);
                          } else {
                            setSelectedGroups(selectedGroups.filter(id => id !== group.id));
                          }
                        }}
                      />
                      <span>{group.name} ({group.member_count || 0} members)</span>
                    </label>
                  ))
                )}
              </div>
            </div>
          )}

          {assignmentType === 'individual' && selectedClass && (
            <div className="form-section">
              <h3>Select Students</h3>
              <div className="selection-grid">
                {students.length === 0 ? (
                  <p className="no-data-message">No students found for this class.</p>
                ) : (
                  students.map(student => (
                    <label key={student.id} className="checkbox-option">
                      <input
                        type="checkbox"
                        checked={selectedStudents.includes(student.id)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setSelectedStudents([...selectedStudents, student.id]);
                          } else {
                            setSelectedStudents(selectedStudents.filter(id => id !== student.id));
                          }
                        }}
                      />
                      <span>{student.first_name} {student.last_name} ({student.student_id})</span>
                    </label>
                  ))
                )}
              </div>
            </div>
          )}

          <div className="modal-footer">
            <button type="button" className="btn btn-secondary" onClick={onClose}>
              Cancel
            </button>
            <button type="submit" className="btn btn-primary" disabled={loading}>
              {loading ? 'Assigning...' : 'Assign'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

// Edit Assignment Modal Component
interface EditAssignmentModalProps {
  assignment: Assignment;
  onClose: () => void;
  onSuccess: () => void;
  classes: any[];
  groups: any[];
}

const EditAssignmentModal: React.FC<EditAssignmentModalProps> = ({
  assignment,
  onClose,
  onSuccess,
  classes,
  groups
}) => {
  const { showError } = useNotification();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    status: assignment.status || 'assigned'
  });

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const token = localStorage.getItem('token');

      // Update the assignment status
      const response = await fetch(`/api/assignments/${assignment.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          status: formData.status
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update assignment');
      }

      onSuccess();
    } catch (error) {
      console.error('Error updating assignment:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to update assignment. Please try again.';
      showError('Update Failed', errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="modal edit-assignment-modal" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h2>Edit Assignment</h2>
          <button className="close-btn" onClick={onClose}>×</button>
        </div>

        <form onSubmit={handleSubmit} className="modal-content">
          <div className="form-section">
            <h3>Assignment Details</h3>
            <div className="assignment-info">
              <p><strong>Assignment:</strong> {assignment.scheduleTitle}</p>
              <p><strong>Lab:</strong> {assignment.labName}</p>
              <p><strong>Class:</strong> {assignment.className}</p>
              <p><strong>Assigned To:</strong> {
                assignment.assignmentType === 'class' ? 'Entire Class' :
                assignment.assignmentType === 'group' ? assignment.groupName :
                assignment.studentName
              }</p>
              <p><strong>Date:</strong> {new Date(assignment.scheduledDate).toLocaleDateString()}</p>
            </div>
          </div>

          <div className="form-section">
            <h3>Assignment Status</h3>
            <div className="form-group">
              <label htmlFor="edit-status">Status</label>
              <select
                id="edit-status"
                value={formData.status}
                onChange={(e) => handleInputChange('status', e.target.value)}
              >
                <option value="assigned">Assigned</option>
                <option value="in_progress">In Progress</option>
                <option value="completed">Completed</option>
                <option value="cancelled">Cancelled</option>
              </select>
            </div>
          </div>

          <div className="modal-footer">
            <button type="button" className="btn btn-secondary" onClick={onClose}>
              Cancel
            </button>
            <button type="submit" className="btn btn-primary" disabled={loading}>
              {loading ? 'Updating...' : 'Update Assignment'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AssignmentManagement;
