import React, { useState, useEffect, useCallback } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { useNotification } from '../../contexts/NotificationContext';
import Pagination from '../common/Pagination';
import './Assignments.css';

interface Schedule {
  id: string;
  title: string;
  description: string;
  labId: string;
  labName: string;
  classId: string;
  className: string;
  instructorName: string;
  scheduledDate: string;
  durationMinutes: number;
  deadline: string;
  status: string;
  assignmentType: string;
  createdAt: string;
}

const AssignmentCreation: React.FC = () => {
  const { user } = useAuth();
  const { showSuccess, showError } = useNotification();
  const [schedules, setSchedules] = useState<Schedule[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [classFilter, setClassFilter] = useState('');
  const [labFilter, setLabFilter] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [classes, setClasses] = useState<any[]>([]);
  const [labs, setLabs] = useState<any[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Modal states
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingSchedule, setEditingSchedule] = useState<Schedule | null>(null);

  useEffect(() => {
    fetchSchedules();
    fetchClasses();
    fetchLabs();
  }, []);

  const fetchSchedules = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      const response = await fetch('/api/schedules', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setSchedules(data.schedules || []);
      } else {
        console.error('Failed to fetch schedules');
        setSchedules([]);
      }
    } catch (error) {
      console.error('Error fetching schedules:', error);
      setSchedules([]);
    } finally {
      setLoading(false);
    }
  };

  const fetchClasses = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/classes', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setClasses(data.classes || []);
      }
    } catch (error) {
      console.error('Error fetching classes:', error);
    }
  };

  const fetchLabs = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/labs', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setLabs(data.labs || []);
      }
    } catch (error) {
      console.error('Error fetching labs:', error);
    }
  };

  const handleEditSchedule = (schedule: Schedule) => {
    setEditingSchedule(schedule);
    setShowEditModal(true);
  };

  const handleDeleteSchedule = async (scheduleId: string) => {
    if (!window.confirm('Are you sure you want to delete this assignment? This action cannot be undone.')) {
      return;
    }

    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`/api/schedules/${scheduleId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        showSuccess('Assignment Deleted', 'Assignment has been deleted successfully');
        fetchSchedules();
      } else {
        const errorData = await response.json();
        showError('Delete Failed', errorData.error || 'Failed to delete assignment');
      }
    } catch (error) {
      console.error('Error deleting schedule:', error);
      showError('Delete Failed', 'Failed to delete assignment. Please try again.');
    }
  };

  // Filter schedules based on search and filters
  const filteredSchedules = schedules.filter(schedule => {
    const matchesSearch = schedule.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         schedule.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesClass = !classFilter || schedule.classId === classFilter;
    const matchesLab = !labFilter || schedule.labId === labFilter;
    const matchesStatus = !statusFilter || schedule.status === statusFilter;

    return matchesSearch && matchesClass && matchesLab && matchesStatus;
  });

  // Pagination
  const totalPages = Math.ceil(filteredSchedules.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedSchedules = filteredSchedules.slice(startIndex, startIndex + itemsPerPage);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled': return '#007bff';
      case 'in_progress': return '#28a745';
      case 'completed': return '#6c757d';
      case 'cancelled': return '#dc3545';
      default: return '#6c757d';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  if (loading) {
    return (
      <div className="assignments">
        <div className="loading-container">
          <div className="loading-spinner">Loading assignments...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="assignments">
      <div className="assignments-header">
        <h1>Assignment Creation</h1>
        <p>Create and manage basic assignment schedules. Use Assignment Management to assign them to groups or individuals.</p>
      </div>

      {/* Create New Assignment */}
      {(user?.role === 'instructor' || user?.role === 'admin') && (
        <div className="create-assignment-section">
          <div className="create-assignment-header">
            <h3>Create New Assignment</h3>
            <button
              className="btn btn-primary"
              onClick={() => setShowCreateModal(true)}
            >
              + New Assignment
            </button>
          </div>
          <p className="create-assignment-note">
            Create basic assignment schedules. After creation, use <strong>Assignment Management</strong> to assign them to specific groups or students.
          </p>
        </div>
      )}

      {/* Search and Filters */}
      <div className="assignments-controls">
        <div className="search-section">
          <input
            type="text"
            placeholder="Search assignments..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="search-input"
          />
        </div>

        <div className="filter-group">
          <label htmlFor="classFilter">Filter by Class:</label>
          <select
            id="classFilter"
            value={classFilter}
            onChange={(e) => setClassFilter(e.target.value)}
            className="filter-select"
          >
            <option value="">All Classes</option>
            {classes.map(cls => (
              <option key={cls.id} value={cls.id}>{cls.name}</option>
            ))}
          </select>
        </div>

        <div className="filter-group">
          <label htmlFor="labFilter">Filter by Lab:</label>
          <select
            id="labFilter"
            value={labFilter}
            onChange={(e) => setLabFilter(e.target.value)}
            className="filter-select"
          >
            <option value="">All Labs</option>
            {labs.map(lab => (
              <option key={lab.id} value={lab.id}>{lab.name}</option>
            ))}
          </select>
        </div>

        <div className="filter-group">
          <label htmlFor="statusFilter">Filter by Status:</label>
          <select
            id="statusFilter"
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="filter-select"
          >
            <option value="">All Statuses</option>
            <option value="scheduled">Scheduled</option>
            <option value="in_progress">In Progress</option>
            <option value="completed">Completed</option>
            <option value="cancelled">Cancelled</option>
          </select>
        </div>
      </div>

      {/* Assignments Table */}
      <div className="assignments-table-container">
        <table className="assignments-table">
          <thead>
            <tr>
              <th>Assignment</th>
              <th>Lab</th>
              <th>Class</th>
              <th>Scheduled Date</th>
              <th>Duration</th>
              <th>Deadline</th>
              <th>Status</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {paginatedSchedules.length === 0 ? (
              <tr>
                <td colSpan={8} className="no-assignments">
                  <div className="no-data">
                    <h3>No assignments found</h3>
                    <p>No assignments match your current filters.</p>
                  </div>
                </td>
              </tr>
            ) : (
              paginatedSchedules.map((schedule) => (
                <tr key={schedule.id} className="assignment-row">
                  <td className="assignment-title">
                    <div className="title-cell">
                      <h4>{schedule.title}</h4>
                      {schedule.description && <p>{schedule.description}</p>}
                    </div>
                  </td>
                  <td>{schedule.labName}</td>
                  <td>{schedule.className || 'Not assigned'}</td>
                  <td>
                    <div>
                      <div>{formatDate(schedule.scheduledDate)}</div>
                      <small>{formatTime(schedule.scheduledDate)}</small>
                    </div>
                  </td>
                  <td>{schedule.durationMinutes} min</td>
                  <td>{schedule.deadline ? formatDate(schedule.deadline) : 'No deadline'}</td>
                  <td>
                    <span 
                      className="status-badge"
                      style={{ backgroundColor: getStatusColor(schedule.status) }}
                    >
                      {schedule.status.replace('_', ' ').toUpperCase()}
                    </span>
                  </td>
                  <td className="assignment-actions">
                    {user?.role === 'instructor' && (
                      <>
                        <button
                          className="btn btn-sm btn-primary"
                          onClick={() => handleEditSchedule(schedule)}
                          title="Edit Assignment"
                        >
                          ✏️ Edit
                        </button>
                        <button
                          className="btn btn-sm btn-danger"
                          onClick={() => handleDeleteSchedule(schedule.id)}
                          title="Delete Assignment"
                        >
                          🗑️ Delete
                        </button>
                      </>
                    )}
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {filteredSchedules.length > 0 && (
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          totalItems={filteredSchedules.length}
          itemsPerPage={itemsPerPage}
          onPageChange={setCurrentPage}
          onItemsPerPageChange={(newItemsPerPage) => {
            setItemsPerPage(newItemsPerPage);
            setCurrentPage(1);
          }}
          showItemsPerPage={true}
          showJumpToPage={true}
          itemsPerPageOptions={[5, 10, 20, 50]}
        />
      )}

      {/* Create Assignment Modal */}
      {showCreateModal && (
        <CreateAssignmentModal
          onClose={() => setShowCreateModal(false)}
          onSuccess={() => {
            setShowCreateModal(false);
            fetchSchedules();
            showSuccess('Assignment Created', 'Assignment has been created successfully');
          }}
          classes={classes}
          labs={labs}
        />
      )}

      {/* Edit Assignment Modal */}
      {showEditModal && editingSchedule && (
        <EditAssignmentModal
          schedule={editingSchedule}
          onClose={() => {
            setShowEditModal(false);
            setEditingSchedule(null);
          }}
          onSuccess={() => {
            setShowEditModal(false);
            setEditingSchedule(null);
            fetchSchedules();
            showSuccess('Assignment Updated', 'Assignment has been updated successfully');
          }}
          classes={classes}
          labs={labs}
        />
      )}
    </div>
  );
};

// Create Assignment Modal Component
interface CreateAssignmentModalProps {
  onClose: () => void;
  onSuccess: () => void;
  classes: any[];
  labs: any[];
}

const CreateAssignmentModal: React.FC<CreateAssignmentModalProps> = ({
  onClose,
  onSuccess,
  classes,
  labs
}) => {
  const { showError } = useNotification();
  const [loading, setLoading] = useState(false);

  // Helper function to get default values
  const getDefaultValues = () => {
    const now = new Date();
    const tomorrow = new Date(now);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const defaultDate = tomorrow.toISOString().split('T')[0];
    const defaultTime = '09:00';
    const defaultDuration = 120;

    return {
      scheduledDate: defaultDate,
      startTime: defaultTime,
      durationMinutes: defaultDuration,
      deadline: calculateDeadline(defaultDate)
    };
  };

  // Helper function to calculate deadline (7 days after scheduled date)
  const calculateDeadline = useCallback((scheduledDate: string) => {
    if (!scheduledDate) return '';
    const scheduleDate = new Date(scheduledDate);
    scheduleDate.setDate(scheduleDate.getDate() + 7);
    return scheduleDate.toISOString().split('T')[0]; // YYYY-MM-DD format
  }, []);

  // Helper function to calculate end time
  const calculateEndTime = useCallback((startTime: string, durationMinutes: number) => {
    if (!startTime || !durationMinutes) return '';

    const [hours, minutes] = startTime.split(':').map(Number);
    const startDate = new Date();
    startDate.setHours(hours, minutes, 0, 0);

    const endDate = new Date(startDate.getTime() + durationMinutes * 60000);
    return endDate.toTimeString().slice(0, 5); // HH:MM format
  }, []);

  const [formData, setFormData] = useState({
    title: '',
    description: '',
    labId: '',
    classId: '',
    assignmentType: 'class' as 'class' | 'group' | 'individual',
    ...getDefaultValues()
  });

  // Auto-calculate end time when start time or duration changes
  useEffect(() => {
    if (formData.startTime && formData.durationMinutes) {
      const newEndTime = calculateEndTime(formData.startTime, formData.durationMinutes);
      if (newEndTime !== formData.endTime) {
        setFormData(prev => ({
          ...prev,
          endTime: newEndTime
        }));
      }
    }
  }, [formData.startTime, formData.durationMinutes, calculateEndTime]);

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => {
      const newData = {
        ...prev,
        [field]: value
      };

      // Auto-update deadline when scheduled date changes
      if (field === 'scheduledDate') {
        newData.deadline = calculateDeadline(value);
      }

      return newData;
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const token = localStorage.getItem('token');

      // Create the schedule
      const scheduleData = {
        title: formData.title,
        description: formData.description,
        lab_id: formData.labId,
        class_id: formData.classId || null,
        scheduled_date: formData.scheduledDate,
        start_time: formData.startTime,
        end_time: formData.endTime || calculateEndTime(formData.startTime, formData.durationMinutes),
        duration_minutes: formData.durationMinutes,
        deadline: formData.deadline,
        assignment_type: formData.assignmentType
      };

      const scheduleResponse = await fetch('/api/schedules', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(scheduleData)
      });

      if (!scheduleResponse.ok) {
        const errorData = await scheduleResponse.json();
        console.log('Schedule creation failed:', scheduleResponse.status, errorData);
        if (scheduleResponse.status === 409 && errorData.suggestedTimes) {
          // Handle scheduling conflict with suggestions
          const suggestions = errorData.suggestedTimes
            .map((time: any) => `${time.start_time}-${time.end_time}`)
            .join(', ');
          throw new Error(`Scheduling conflict: ${errorData.message || 'Time slot is already taken'}. Available times: ${suggestions}`);
        } else if (scheduleResponse.status === 409) {
          throw new Error(errorData.error || 'Scheduling conflict detected. Please choose a different time.');
        } else {
          throw new Error(errorData.error || 'Failed to create schedule');
        }
      }

      onSuccess();
    } catch (error) {
      console.error('Error creating assignment:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to create assignment. Please try again.';
      showError('Creation Failed', errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="modal create-assignment-modal" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h2>Create New Assignment</h2>
          <button className="close-btn" onClick={onClose}>×</button>
        </div>

        <form onSubmit={handleSubmit} className="modal-content">
          <div className="form-section">
            <h3>Assignment Information</h3>
            <div className="form-grid">
              <div className="form-group">
                <label htmlFor="title">Assignment Title *</label>
                <input
                  type="text"
                  id="title"
                  value={formData.title}
                  onChange={(e) => handleInputChange('title', e.target.value)}
                  required
                  placeholder="Enter assignment title"
                />
              </div>

              <div className="form-group full-width">
                <label htmlFor="description">Description</label>
                <textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder="Enter assignment description"
                  rows={3}
                />
              </div>
            </div>
          </div>

          <div className="form-section">
            <h3>Lab and Class</h3>
            <div className="form-grid">
              <div className="form-group">
                <label htmlFor="labId">Lab *</label>
                <select
                  id="labId"
                  value={formData.labId}
                  onChange={(e) => handleInputChange('labId', e.target.value)}
                  required
                >
                  <option value="">Select a lab</option>
                  {labs.map(lab => (
                    <option key={lab.id} value={lab.id}>{lab.name}</option>
                  ))}
                </select>
              </div>

              <div className="form-group">
                <label htmlFor="classId">Class (Optional)</label>
                <select
                  id="classId"
                  value={formData.classId}
                  onChange={(e) => handleInputChange('classId', e.target.value)}
                >
                  <option value="">No specific class</option>
                  {classes.map(cls => (
                    <option key={cls.id} value={cls.id}>{cls.name}</option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          <div className="form-section">
            <h3>Schedule Details</h3>
            <div className="form-grid">
              <div className="form-group">
                <label htmlFor="scheduledDate">Scheduled Date *</label>
                <input
                  type="date"
                  id="scheduledDate"
                  value={formData.scheduledDate}
                  onChange={(e) => handleInputChange('scheduledDate', e.target.value)}
                  required
                />
              </div>

              <div className="form-group">
                <label htmlFor="startTime">Start Time *</label>
                <input
                  type="time"
                  id="startTime"
                  value={formData.startTime}
                  onChange={(e) => handleInputChange('startTime', e.target.value)}
                  required
                />
              </div>

              <div className="form-group">
                <label htmlFor="durationMinutes">Duration (minutes) *</label>
                <input
                  type="number"
                  id="durationMinutes"
                  value={formData.durationMinutes}
                  onChange={(e) => handleInputChange('durationMinutes', parseInt(e.target.value))}
                  min="30"
                  max="480"
                  step="30"
                  required
                />
              </div>

              <div className="form-group">
                <label htmlFor="deadline">Deadline</label>
                <input
                  type="date"
                  id="deadline"
                  value={formData.deadline}
                  onChange={(e) => handleInputChange('deadline', e.target.value)}
                />
              </div>
            </div>
          </div>

          <div className="modal-footer">
            <button type="button" className="btn btn-secondary" onClick={onClose}>
              Cancel
            </button>
            <button type="submit" className="btn btn-primary" disabled={loading}>
              {loading ? 'Creating...' : 'Create Assignment'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

// Edit Assignment Modal Component
interface EditAssignmentModalProps {
  schedule: Schedule;
  onClose: () => void;
  onSuccess: () => void;
  classes: any[];
  labs: any[];
}

const EditAssignmentModal: React.FC<EditAssignmentModalProps> = ({
  schedule,
  onClose,
  onSuccess,
  classes,
  labs
}) => {
  const { showError } = useNotification();
  const [loading, setLoading] = useState(false);

  const [formData, setFormData] = useState({
    title: schedule.title || '',
    description: schedule.description || '',
    labId: schedule.labId || '',
    classId: schedule.classId || '',
    scheduledDate: schedule.scheduledDate ? schedule.scheduledDate.split('T')[0] : '',
    startTime: schedule.scheduledDate ? new Date(schedule.scheduledDate).toTimeString().slice(0, 5) : '',
    durationMinutes: schedule.durationMinutes || 120,
    deadline: schedule.deadline ? schedule.deadline.split('T')[0] : '',
    status: schedule.status || 'scheduled'
  });

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const token = localStorage.getItem('token');

      // Update the schedule
      const scheduleResponse = await fetch(`/api/schedules/${schedule.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          title: formData.title,
          description: formData.description,
          lab_id: formData.labId,
          class_id: formData.classId || null,
          scheduled_date: formData.scheduledDate,
          start_time: formData.startTime,
          duration_minutes: formData.durationMinutes,
          deadline: formData.deadline,
          status: formData.status
        })
      });

      if (!scheduleResponse.ok) {
        const errorData = await scheduleResponse.json();
        throw new Error(errorData.error || 'Failed to update schedule');
      }

      onSuccess();
    } catch (error) {
      console.error('Error updating assignment:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to update assignment. Please try again.';
      showError('Update Failed', errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="modal edit-assignment-modal" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h2>Edit Assignment</h2>
          <button className="close-btn" onClick={onClose}>×</button>
        </div>

        <form onSubmit={handleSubmit} className="modal-content">
          <div className="form-section">
            <h3>Assignment Information</h3>
            <div className="form-grid">
              <div className="form-group">
                <label htmlFor="edit-title">Assignment Title *</label>
                <input
                  type="text"
                  id="edit-title"
                  value={formData.title}
                  onChange={(e) => handleInputChange('title', e.target.value)}
                  required
                  placeholder="Enter assignment title"
                />
              </div>

              <div className="form-group full-width">
                <label htmlFor="edit-description">Description</label>
                <textarea
                  id="edit-description"
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder="Enter assignment description"
                  rows={3}
                />
              </div>
            </div>
          </div>

          <div className="form-section">
            <h3>Lab and Class</h3>
            <div className="form-grid">
              <div className="form-group">
                <label htmlFor="edit-labId">Lab *</label>
                <select
                  id="edit-labId"
                  value={formData.labId}
                  onChange={(e) => handleInputChange('labId', e.target.value)}
                  required
                >
                  <option value="">Select a lab</option>
                  {labs.map(lab => (
                    <option key={lab.id} value={lab.id}>{lab.name}</option>
                  ))}
                </select>
              </div>

              <div className="form-group">
                <label htmlFor="edit-classId">Class (Optional)</label>
                <select
                  id="edit-classId"
                  value={formData.classId}
                  onChange={(e) => handleInputChange('classId', e.target.value)}
                >
                  <option value="">No specific class</option>
                  {classes.map(cls => (
                    <option key={cls.id} value={cls.id}>{cls.name}</option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          <div className="form-section">
            <h3>Schedule Details</h3>
            <div className="form-grid">
              <div className="form-group">
                <label htmlFor="edit-scheduledDate">Scheduled Date *</label>
                <input
                  type="date"
                  id="edit-scheduledDate"
                  value={formData.scheduledDate}
                  onChange={(e) => handleInputChange('scheduledDate', e.target.value)}
                  required
                />
              </div>

              <div className="form-group">
                <label htmlFor="edit-startTime">Start Time *</label>
                <input
                  type="time"
                  id="edit-startTime"
                  value={formData.startTime}
                  onChange={(e) => handleInputChange('startTime', e.target.value)}
                  required
                />
              </div>

              <div className="form-group">
                <label htmlFor="edit-durationMinutes">Duration (minutes) *</label>
                <input
                  type="number"
                  id="edit-durationMinutes"
                  value={formData.durationMinutes}
                  onChange={(e) => handleInputChange('durationMinutes', parseInt(e.target.value))}
                  min="30"
                  max="480"
                  step="30"
                  required
                />
              </div>

              <div className="form-group">
                <label htmlFor="edit-deadline">Deadline</label>
                <input
                  type="date"
                  id="edit-deadline"
                  value={formData.deadline}
                  onChange={(e) => handleInputChange('deadline', e.target.value)}
                />
              </div>

              <div className="form-group">
                <label htmlFor="edit-status">Status</label>
                <select
                  id="edit-status"
                  value={formData.status}
                  onChange={(e) => handleInputChange('status', e.target.value)}
                >
                  <option value="scheduled">Scheduled</option>
                  <option value="in_progress">In Progress</option>
                  <option value="completed">Completed</option>
                  <option value="cancelled">Cancelled</option>
                </select>
              </div>
            </div>
          </div>

          <div className="modal-footer">
            <button type="button" className="btn btn-secondary" onClick={onClose}>
              Cancel
            </button>
            <button type="submit" className="btn btn-primary" disabled={loading}>
              {loading ? 'Updating...' : 'Update Assignment'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AssignmentCreation;
