const express = require('express');
const router = express.Router();
const { query } = require('../config/database');
const { authenticateToken } = require('../middleware/auth');
const multer = require('multer');
const path = require('path');
const fs = require('fs');

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = 'uploads/assignments';
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    // Generate unique filename
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, 'assignment-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB limit
  },
  fileFilter: function (req, file, cb) {
    if (file.mimetype === 'application/pdf') {
      cb(null, true);
    } else {
      cb(new Error('Only PDF files are allowed'));
    }
  }
});

// Get all created assignments
router.get('/', authenticateToken, async (req, res) => {
  try {
    const assignments = await query(`
      SELECT 
        ca.id,
        ca.name,
        ca.description,
        ca.pdf_filename,
        ca.pdf_file_size,
        ca.creation_date,
        ca.status,
        ca.created_at,
        ca.updated_at,
        CONCAT(u.first_name, ' ', u.last_name) as instructor_name,
        u.id as created_by
      FROM created_assignments ca
      LEFT JOIN users u ON ca.created_by = u.id
      ORDER BY ca.created_at DESC
    `);

    res.json({
      success: true,
      assignments: assignments.rows
    });
  } catch (error) {
    console.error('Error fetching created assignments:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch assignments'
    });
  }
});

// Create a new assignment
router.post('/', authenticateToken, upload.single('pdf_file'), async (req, res) => {
  try {
    const { name, description, status } = req.body;
    const userId = req.user.id;

    if (!name) {
      return res.status(400).json({
        success: false,
        error: 'Assignment name is required'
      });
    }

    let pdfFilename = null;
    let pdfFileSize = null;

    if (req.file) {
      pdfFilename = req.file.filename;
      pdfFileSize = req.file.size;
    }

    const result = await query(`
      INSERT INTO created_assignments (
        name, description, pdf_filename, pdf_file_size,
        creation_date, status, created_by
      ) VALUES ($1, $2, $3, $4, $5, $6, $7)
      RETURNING *
    `, [
      name,
      description || null,
      pdfFilename,
      pdfFileSize,
      new Date().toISOString().split('T')[0], // Always use current date
      status || 'draft',
      userId
    ]);

    res.status(201).json({
      success: true,
      assignment: result.rows[0]
    });
  } catch (error) {
    console.error('Error creating assignment:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create assignment'
    });
  }
});

// Update an assignment
router.put('/:id', authenticateToken, upload.single('pdf_file'), async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description, status } = req.body;
    const userId = req.user.id;

    // Check if assignment exists and user has permission
    const existingAssignment = await query(`
      SELECT * FROM created_assignments 
      WHERE id = $1 AND (created_by = $2 OR $3 = 'admin')
    `, [id, userId, req.user.role]);

    if (existingAssignment.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Assignment not found or access denied'
      });
    }

    let updateFields = [];
    let updateValues = [];
    let paramIndex = 1;

    if (name !== undefined) {
      updateFields.push(`name = $${paramIndex}`);
      updateValues.push(name);
      paramIndex++;
    }

    if (description !== undefined) {
      updateFields.push(`description = $${paramIndex}`);
      updateValues.push(description);
      paramIndex++;
    }

    if (status !== undefined) {
      updateFields.push(`status = $${paramIndex}`);
      updateValues.push(status);
      paramIndex++;
    }

    // Handle file upload
    if (req.file) {
      // Delete old file if it exists
      const oldAssignment = existingAssignment.rows[0];
      if (oldAssignment.pdf_filename) {
        const oldFilePath = path.join('uploads/assignments', oldAssignment.pdf_filename);
        if (fs.existsSync(oldFilePath)) {
          fs.unlinkSync(oldFilePath);
        }
      }

      updateFields.push(`pdf_filename = $${paramIndex}`);
      updateValues.push(req.file.filename);
      paramIndex++;

      updateFields.push(`pdf_file_size = $${paramIndex}`);
      updateValues.push(req.file.size);
      paramIndex++;
    }

    updateFields.push(`updated_at = CURRENT_TIMESTAMP`);
    updateValues.push(id);

    const updateQuery = `
      UPDATE created_assignments 
      SET ${updateFields.join(', ')}
      WHERE id = $${paramIndex}
      RETURNING *
    `;

    const result = await query(updateQuery, updateValues);

    res.json({
      success: true,
      assignment: result.rows[0]
    });
  } catch (error) {
    console.error('Error updating assignment:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update assignment'
    });
  }
});

// Delete an assignment
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    // Check if assignment exists and user has permission
    const existingAssignment = await query(`
      SELECT * FROM created_assignments 
      WHERE id = $1 AND (created_by = $2 OR $3 = 'admin')
    `, [id, userId, req.user.role]);

    if (existingAssignment.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Assignment not found or access denied'
      });
    }

    // Delete associated file
    const assignment = existingAssignment.rows[0];
    if (assignment.pdf_filename) {
      const filePath = path.join('uploads/assignments', assignment.pdf_filename);
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }
    }

    // Delete assignment from database
    await query('DELETE FROM created_assignments WHERE id = $1', [id]);

    res.json({
      success: true,
      message: 'Assignment deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting assignment:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete assignment'
    });
  }
});

// Download assignment PDF
router.get('/:id/download', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;

    const assignment = await query(`
      SELECT pdf_filename, name FROM created_assignments WHERE id = $1
    `, [id]);

    if (assignment.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Assignment not found'
      });
    }

    const assignmentData = assignment.rows[0];
    if (!assignmentData.pdf_filename) {
      return res.status(404).json({
        success: false,
        error: 'No PDF file found for this assignment'
      });
    }

    const filePath = path.join('uploads/assignments', assignmentData.pdf_filename);
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({
        success: false,
        error: 'PDF file not found on server'
      });
    }

    res.download(filePath, `${assignmentData.name}.pdf`);
  } catch (error) {
    console.error('Error downloading assignment:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to download assignment'
    });
  }
});

module.exports = router;
